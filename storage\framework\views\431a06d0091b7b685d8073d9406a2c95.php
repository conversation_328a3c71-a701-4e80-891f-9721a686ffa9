<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management - Admin Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .admin-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .admin-header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .admin-header h1 {
            font-size: 28px;
        }

        .back-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 6px;
            transition: background 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .content {
            padding: 40px;
        }

        .users-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .users-table th,
        .users-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #ecf0f1;
        }

        .users-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
            text-transform: uppercase;
            font-size: 12px;
            letter-spacing: 1px;
        }

        .users-table tr:hover {
            background: #f8f9fa;
        }

        .user-name {
            font-weight: 600;
            color: #2c3e50;
        }

        .user-email {
            color: #7f8c8d;
            font-size: 14px;
        }

        .pet-count {
            background: #3498db;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }

        .join-date {
            color: #95a5a6;
            font-size: 14px;
        }

        .pagination {
            margin-top: 30px;
            display: flex;
            justify-content: center;
        }

        .pagination a,
        .pagination span {
            padding: 8px 12px;
            margin: 0 4px;
            text-decoration: none;
            border-radius: 4px;
            color: #3498db;
            border: 1px solid #ecf0f1;
        }

        .pagination .current {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        .pagination a:hover {
            background: #f8f9fa;
        }

        .stats-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 4px solid #3498db;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            display: block;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1>👥 User Management</h1>
            <a href="<?php echo e(route('admin.dashboard')); ?>" class="back-btn">← Back to Dashboard</a>
        </div>

        <div class="content">
            <div class="stats-summary">
                <div class="stat-card">
                    <span class="stat-number"><?php echo e($users->total()); ?></span>
                    <span class="stat-label">Total Users</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number"><?php echo e($users->where('pet_health_records_count', '>', 0)->count()); ?></span>
                    <span class="stat-label">Active Users</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number"><?php echo e($users->where('pet_health_records_count', '>', 1)->count()); ?></span>
                    <span class="stat-label">Multi-Pet Owners</span>
                </div>
            </div>

            <table class="users-table">
                <thead>
                    <tr>
                        <th>User</th>
                        <th>Pet Count</th>
                        <th>Latest Pet Species</th>
                        <th>Join Date</th>
                        <th>Last Activity</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td>
                                <div class="user-name"><?php echo e($user->name); ?></div>
                                <div class="user-email"><?php echo e($user->email); ?></div>
                            </td>
                            <td>
                                <span class="pet-count"><?php echo e($user->pet_health_records_count); ?></span>
                            </td>
                            <td>
                                <?php if($user->petHealthRecords->isNotEmpty()): ?>
                                    <?php echo e($user->petHealthRecords->first()->species); ?>

                                <?php else: ?>
                                    <span style="color: #95a5a6;">No pets</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="join-date"><?php echo e($user->created_at->format('M d, Y')); ?></div>
                            </td>
                            <td>
                                <div class="join-date"><?php echo e($user->created_at->diffForHumans()); ?></div>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="5" style="text-align: center; color: #95a5a6; padding: 40px;">
                                No users found
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>

            <div class="pagination">
                <?php echo e($users->links()); ?>

            </div>
        </div>
    </div>
</body>
</html>
<?php /**PATH C:\Users\<USER>\pawportal\resources\views/admin/users.blade.php ENDPATH**/ ?>