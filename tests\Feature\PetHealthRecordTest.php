<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use App\Models\User;
use App\Models\PetHealthRecord;

class PetHealthRecordTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user
        $this->user = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'is_admin' => false,
        ]);
    }

    public function test_user_can_view_pet_health_index()
    {
        $response = $this->actingAs($this->user)->get('/pet-health');
        
        $response->assertStatus(200);
        $response->assertSee('My Pet Health Records');
    }

    public function test_user_can_view_create_form()
    {
        $response = $this->actingAs($this->user)->get('/pet-health/create');
        
        $response->assertStatus(200);
        $response->assertSee('Add Pet Health Record');
    }

    public function test_user_can_create_pet_health_record()
    {
        $petData = [
            'pet_name' => 'Test Pet',
            'species' => 'Dog',
            'breed' => 'Test Breed',
            'medical_history' => 'Test medical history',
            'last_vaccination' => '2024-01-01',
        ];

        $response = $this->actingAs($this->user)->post('/pet-health', $petData);
        
        $response->assertRedirect('/pet-health');
        $response->assertSessionHas('success', 'Pet health record saved successfully!');
        
        $this->assertDatabaseHas('pet_health_records', [
            'user_id' => $this->user->id,
            'pet_name' => 'Test Pet',
            'species' => 'Dog',
        ]);
    }

    public function test_user_can_edit_their_own_record()
    {
        $record = PetHealthRecord::factory()->create([
            'user_id' => $this->user->id,
            'pet_name' => 'Original Name',
        ]);

        $response = $this->actingAs($this->user)->get("/pet-health/{$record->id}/edit");
        
        $response->assertStatus(200);
        $response->assertSee('Edit Pet Health Record');
        $response->assertSee('Original Name');
    }

    public function test_user_can_update_their_own_record()
    {
        $record = PetHealthRecord::factory()->create([
            'user_id' => $this->user->id,
            'pet_name' => 'Original Name',
        ]);

        $updateData = [
            'pet_name' => 'Updated Name',
            'species' => 'Cat',
            'breed' => 'Updated Breed',
            'medical_history' => 'Updated history',
            'last_vaccination' => '2024-02-01',
        ];

        $response = $this->actingAs($this->user)->put("/pet-health/{$record->id}", $updateData);
        
        $response->assertRedirect('/pet-health');
        $response->assertSessionHas('success', 'Pet health record updated successfully!');
        
        $this->assertDatabaseHas('pet_health_records', [
            'id' => $record->id,
            'pet_name' => 'Updated Name',
            'species' => 'Cat',
        ]);
    }

    public function test_user_can_delete_their_own_record()
    {
        $record = PetHealthRecord::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $response = $this->actingAs($this->user)->delete("/pet-health/{$record->id}");
        
        $response->assertRedirect('/pet-health');
        $response->assertSessionHas('success', 'Pet health record deleted successfully!');
        
        $this->assertDatabaseMissing('pet_health_records', [
            'id' => $record->id,
        ]);
    }

    public function test_user_cannot_access_other_users_records()
    {
        $otherUser = User::factory()->create();
        $record = PetHealthRecord::factory()->create([
            'user_id' => $otherUser->id,
        ]);

        $response = $this->actingAs($this->user)->get("/pet-health/{$record->id}/edit");
        $response->assertStatus(403);

        $response = $this->actingAs($this->user)->put("/pet-health/{$record->id}", [
            'pet_name' => 'Hacked Name',
            'species' => 'Dog',
        ]);
        $response->assertStatus(403);

        $response = $this->actingAs($this->user)->delete("/pet-health/{$record->id}");
        $response->assertStatus(403);
    }

    public function test_validation_works_for_required_fields()
    {
        $response = $this->actingAs($this->user)->post('/pet-health', []);
        
        $response->assertSessionHasErrors(['pet_name', 'species']);
    }
}
