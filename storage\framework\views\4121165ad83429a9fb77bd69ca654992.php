<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Health - Admin Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .admin-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .admin-header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .admin-header h1 {
            font-size: 28px;
        }

        .back-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 6px;
            transition: background 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .content {
            padding: 40px;
        }

        .health-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .health-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #3498db;
        }

        .health-card.database { border-left-color: #2ecc71; }
        .health-card.integrity { border-left-color: #f39c12; }
        .health-card.activity { border-left-color: #e74c3c; }

        .health-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 18px;
        }

        .metric-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #ecf0f1;
        }

        .metric-item:last-child {
            border-bottom: none;
        }

        .metric-label {
            color: #7f8c8d;
            font-size: 14px;
        }

        .metric-value {
            font-weight: 600;
            color: #2c3e50;
        }

        .status-indicator {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-healthy {
            background: #2ecc71;
            color: white;
        }

        .status-warning {
            background: #f39c12;
            color: white;
        }

        .status-error {
            background: #e74c3c;
            color: white;
        }

        .integrity-issues {
            margin-top: 15px;
        }

        .issue-item {
            background: #fff5f5;
            border: 1px solid #fed7d7;
            border-radius: 6px;
            padding: 10px;
            margin-bottom: 8px;
            color: #c53030;
            font-size: 14px;
        }

        .activity-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 15px;
        }

        .activity-stat {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .activity-number {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            display: block;
        }

        .activity-label {
            font-size: 12px;
            color: #7f8c8d;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-top: 5px;
        }

        .refresh-btn {
            background: #3498db;
            color: white;
            padding: 12px 25px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 500;
            transition: background 0.3s ease;
            display: inline-block;
            margin-top: 20px;
        }

        .refresh-btn:hover {
            background: #2980b9;
        }

        .system-overview {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 30px;
            text-align: center;
        }

        .system-overview h2 {
            margin-bottom: 10px;
        }

        .system-overview p {
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1>⚡ System Health</h1>
            <a href="<?php echo e(route('admin.dashboard')); ?>" class="back-btn">← Back to Dashboard</a>
        </div>

        <div class="content">
            <div class="system-overview">
                <h2>System Status Overview</h2>
                <p>Monitor your PawPortal system health and performance metrics</p>
            </div>

            <div class="health-grid">
                <!-- Database Health -->
                <div class="health-card database">
                    <h3>📊 Database Health</h3>
                    <div class="metric-item">
                        <span class="metric-label">Database Size</span>
                        <span class="metric-value"><?php echo e($metrics['database_size']); ?></span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-label">Total Records</span>
                        <span class="metric-value"><?php echo e(number_format($metrics['total_records'])); ?></span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-label">Status</span>
                        <span class="status-indicator status-healthy">Healthy</span>
                    </div>
                </div>

                <!-- Data Integrity -->
                <div class="health-card integrity">
                    <h3>🔍 Data Integrity</h3>
                    <div class="metric-item">
                        <span class="metric-label">Overall Status</span>
                        <?php if($metrics['data_integrity']['status'] === 'healthy'): ?>
                            <span class="status-indicator status-healthy">Healthy</span>
                        <?php else: ?>
                            <span class="status-indicator status-warning">Issues Found</span>
                        <?php endif; ?>
                    </div>
                    
                    <?php if($metrics['data_integrity']['status'] !== 'healthy'): ?>
                        <div class="integrity-issues">
                            <strong>Issues Found:</strong>
                            <?php $__currentLoopData = $metrics['data_integrity']['details']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $issue): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="issue-item"><?php echo e($issue); ?></div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- User Activity -->
                <div class="health-card activity">
                    <h3>👥 User Activity</h3>
                    <div class="activity-stats">
                        <div class="activity-stat">
                            <span class="activity-number"><?php echo e($metrics['user_activity']['new_users_today']); ?></span>
                            <span class="activity-label">New Users Today</span>
                        </div>
                        <div class="activity-stat">
                            <span class="activity-number"><?php echo e($metrics['user_activity']['new_users_week']); ?></span>
                            <span class="activity-label">New Users This Week</span>
                        </div>
                        <div class="activity-stat">
                            <span class="activity-number"><?php echo e($metrics['user_activity']['new_pets_today']); ?></span>
                            <span class="activity-label">New Pets Today</span>
                        </div>
                        <div class="activity-stat">
                            <span class="activity-number"><?php echo e($metrics['user_activity']['new_pets_week']); ?></span>
                            <span class="activity-label">New Pets This Week</span>
                        </div>
                    </div>
                </div>
            </div>

            <div style="text-align: center;">
                <a href="<?php echo e(route('admin.system-health')); ?>" class="refresh-btn">🔄 Refresh Metrics</a>
            </div>
        </div>
    </div>
</body>
</html>
<?php /**PATH C:\Users\<USER>\pawportal\resources\views/admin/system-health.blade.php ENDPATH**/ ?>