<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pet Management - Admin Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .admin-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .admin-header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .admin-header h1 {
            font-size: 28px;
        }

        .back-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 6px;
            transition: background 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .content {
            padding: 40px;
        }

        .stats-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 4px solid #2ecc71;
        }

        .stat-card.health { border-left-color: #f39c12; }
        .stat-card.recent { border-left-color: #3498db; }
        .stat-card.overdue { border-left-color: #e74c3c; }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            display: block;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .pets-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .pets-table th,
        .pets-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #ecf0f1;
        }

        .pets-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
            text-transform: uppercase;
            font-size: 12px;
            letter-spacing: 1px;
        }

        .pets-table tr:hover {
            background: #f8f9fa;
        }

        .pet-name {
            font-weight: 600;
            color: #2c3e50;
        }

        .pet-species {
            color: #7f8c8d;
            font-size: 14px;
        }

        .owner-info {
            color: #7f8c8d;
            font-size: 14px;
        }

        .vaccination-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }

        .vaccination-status.recent {
            background: #2ecc71;
            color: white;
        }

        .vaccination-status.overdue {
            background: #e74c3c;
            color: white;
        }

        .vaccination-status.none {
            background: #95a5a6;
            color: white;
        }

        .registration-date {
            color: #95a5a6;
            font-size: 14px;
        }

        .pagination {
            margin-top: 30px;
            display: flex;
            justify-content: center;
        }

        .pagination a,
        .pagination span {
            padding: 8px 12px;
            margin: 0 4px;
            text-decoration: none;
            border-radius: 4px;
            color: #3498db;
            border: 1px solid #ecf0f1;
        }

        .pagination .current {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        .pagination a:hover {
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1>🐾 Pet Management</h1>
            <a href="<?php echo e(route('admin.dashboard')); ?>" class="back-btn">← Back to Dashboard</a>
        </div>

        <div class="content">
            <div class="stats-summary">
                <div class="stat-card">
                    <span class="stat-number"><?php echo e($vaccinationStats['total']); ?></span>
                    <span class="stat-label">Total Pets</span>
                </div>
                <div class="stat-card health">
                    <span class="stat-number"><?php echo e($vaccinationStats['vaccinated']); ?></span>
                    <span class="stat-label">Vaccinated</span>
                </div>
                <div class="stat-card recent">
                    <span class="stat-number"><?php echo e($vaccinationStats['recent_vaccinations']); ?></span>
                    <span class="stat-label">Recent Shots</span>
                </div>
                <div class="stat-card overdue">
                    <span class="stat-number"><?php echo e($vaccinationStats['overdue_vaccinations']); ?></span>
                    <span class="stat-label">Overdue</span>
                </div>
            </div>

            <table class="pets-table">
                <thead>
                    <tr>
                        <th>Pet Details</th>
                        <th>Owner</th>
                        <th>Vaccination Status</th>
                        <th>Last Vaccination</th>
                        <th>Registration Date</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $pets; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pet): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td>
                                <div class="pet-name"><?php echo e($pet->pet_name); ?></div>
                                <div class="pet-species"><?php echo e($pet->species); ?><?php echo e($pet->breed ? ' - ' . $pet->breed : ''); ?></div>
                            </td>
                            <td>
                                <div class="owner-info"><?php echo e($pet->user->name); ?></div>
                                <div class="owner-info"><?php echo e($pet->user->email); ?></div>
                            </td>
                            <td>
                                <?php if($pet->last_vaccination): ?>
                                    <?php if($pet->last_vaccination >= now()->subMonths(6)): ?>
                                        <span class="vaccination-status recent">Recent</span>
                                    <?php elseif($pet->last_vaccination < now()->subYear()): ?>
                                        <span class="vaccination-status overdue">Overdue</span>
                                    <?php else: ?>
                                        <span class="vaccination-status">Vaccinated</span>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <span class="vaccination-status none">No Record</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if($pet->last_vaccination): ?>
                                    <div class="registration-date"><?php echo e(\Carbon\Carbon::parse($pet->last_vaccination)->format('M d, Y')); ?></div>
                                <?php else: ?>
                                    <span style="color: #95a5a6;">Not recorded</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="registration-date"><?php echo e($pet->created_at->format('M d, Y')); ?></div>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="5" style="text-align: center; color: #95a5a6; padding: 40px;">
                                No pets found
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>

            <div class="pagination">
                <?php echo e($pets->links()); ?>

            </div>
        </div>
    </div>
</body>
</html>
<?php /**PATH C:\Users\<USER>\pawportal\resources\views/admin/pets.blade.php ENDPATH**/ ?>